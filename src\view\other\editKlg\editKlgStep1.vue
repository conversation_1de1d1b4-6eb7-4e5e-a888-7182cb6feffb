<script setup lang="ts">
import inlineCKEditor from '@/components/editors/VeditorInline.vue';
import classicCKEditor from '@/components/editors/Veditor.vue';
import CmpButton from '@/components/CmpButton.vue';
import { editBaseInfoApi } from '@/apis/path/klg';
import type { params2EditBaseInfo } from '@/apis/path/klg';
import { KlgType, KlgTypeDict } from '@/utils/constant';
import { KlgDetail } from '@/utils/type';
import { ElMessage, FormInstance } from 'element-plus';
import { ref, watch, nextTick } from 'vue';
import router from '@/router';
import { useRoute } from 'vue-router';
import { convertLanguageMathToScript, convertImgTagLongUrls } from '@/utils/latexUtils';
import katex from 'katex';
const route = useRoute();
const props = defineProps({
  showStep3: Boolean,
  detail: Object as () => KlgDetail
});
const emits = defineEmits(['step', 'showStep3', 'refresh']);
const infoFormRef = ref<FormInstance>();
const blankInfoForm = ref({
  klgCode: '',
  klgType: '',
  klgName: '',
  synonymList: [] as String[],
  note: ''
});
const infoForm = ref(blankInfoForm); // 基础信息表单
const curKlgCode = ref();
const editorTitle = ref();
const editorNote = ref();

// KaTeX 实时渲染相关
const mathInput = ref(''); // 输入框内容
const renderedMath = ref(''); // 渲染后的数学公式
const isEditMode = ref(true); // 是否为编辑模式
const mathInputRef = ref(); // 输入框引用

// 知识名称编辑/预览模式
const klgNameEditMode = ref(true);
const klgNameRendered = ref('');
const klgNameInputRef = ref();

// 同义词编辑/预览模式
const synonymEditModes = ref<boolean[]>([]);
const synonymRendered = ref<string[]>([]);
const synonymInputRefs = ref<any[]>([]);

// KaTeX 渲染函数
const renderMath = (input: string) => {
  if (!input.trim()) {
    renderedMath.value = '';
    return;
  }

  try {
    // 检测是否包含数学公式标记
    let mathContent = input;
    let displayMode = false;

    // 处理行间公式 $$...$$
    if (input.includes('$$')) {
      mathContent = input.replace(/\$\$(.*?)\$\$/g, '$1');
      displayMode = true;
    }
    // 处理行内公式 $...$
    else if (input.includes('$')) {
      mathContent = input.replace(/\$(.*?)\$/g, '$1');
      displayMode = false;
    }

    // 使用 KaTeX 渲染
    const rendered = katex.renderToString(mathContent, {
      displayMode: displayMode,
      throwOnError: false,
      output: 'html',
      strict: false
    });

    renderedMath.value = rendered;
  } catch (error) {
    console.error('KaTeX渲染错误:', error);
    renderedMath.value = `<span style="color: red;">渲染错误: ${input}</span>`;
  }
};

// 知识名称相关处理函数
const renderKlgName = (input: string) => {
  if (!input.trim()) {
    klgNameRendered.value = '';
    return;
  }

  try {
    let mathContent = input;
    let displayMode = false;

    if (input.includes('$$')) {
      mathContent = input.replace(/\$\$(.*?)\$\$/g, '$1');
      displayMode = true;
    } else if (input.includes('$')) {
      mathContent = input.replace(/\$(.*?)\$/g, '$1');
      displayMode = false;
    }

    const rendered = katex.renderToString(mathContent, {
      displayMode: displayMode,
      throwOnError: false,
      output: 'html',
      strict: false
    });

    klgNameRendered.value = rendered;
  } catch (error) {
    console.error('知识名称KaTeX渲染错误:', error);
    klgNameRendered.value = `<span style="color: red;">渲染错误: ${input}</span>`;
  }
};

// 同义词相关处理函数
const renderSynonym = (input: string, index: number) => {
  if (!input.trim()) {
    synonymRendered.value[index] = '';
    return;
  }

  try {
    let mathContent = input;
    let displayMode = false;

    if (input.includes('$$')) {
      mathContent = input.replace(/\$\$(.*?)\$\$/g, '$1');
      displayMode = true;
    } else if (input.includes('$')) {
      mathContent = input.replace(/\$(.*?)\$/g, '$1');
      displayMode = false;
    }

    const rendered = katex.renderToString(mathContent, {
      displayMode: displayMode,
      throwOnError: false,
      output: 'html',
      strict: false
    });

    synonymRendered.value[index] = rendered;
  } catch (error) {
    console.error(`同义词${index}KaTeX渲染错误:`, error);
    synonymRendered.value[index] = `<span style="color: red;">渲染错误: ${input}</span>`;
  }
};

// 监听输入变化
watch(mathInput, (newValue) => {
  renderMath(newValue);
}, { immediate: true });

// 监听知识名称变化
watch(() => infoForm.value.klgName, (newValue) => {
  renderKlgName(newValue);
}, { immediate: true });

// 监听同义词列表变化
watch(() => infoForm.value.synonymList, (newList) => {
  // 确保数组长度匹配
  while (synonymEditModes.value.length < newList.length) {
    synonymEditModes.value.push(true);
    synonymRendered.value.push('');
    synonymInputRefs.value.push(null);
  }

  // 渲染每个同义词
  newList.forEach((synonym, index) => {
    renderSynonym(synonym as string, index);
  });
}, { deep: true, immediate: true });

// 处理输入框失焦 - 切换到预览模式
const handleInputBlur = () => {
  if (mathInput.value.trim() && renderedMath.value) {
    isEditMode.value = false;
  }
};

// 处理点击预览区域 - 切换到编辑模式
const handlePreviewClick = () => {
  isEditMode.value = true;
  // 使用 nextTick 确保 DOM 更新后再聚焦
  nextTick(() => {
    if (mathInputRef.value) {
      mathInputRef.value.focus();
    }
  });
};

// 处理输入框聚焦 - 确保在编辑模式
const handleInputFocus = () => {
  isEditMode.value = true;
};

// 知识名称相关处理函数
const renderKlgName = (input: string) => {
  if (!input.trim()) {
    klgNameRendered.value = '';
    return;
  }

  try {
    let mathContent = input;
    let displayMode = false;

    if (input.includes('$$')) {
      mathContent = input.replace(/\$\$(.*?)\$\$/g, '$1');
      displayMode = true;
    } else if (input.includes('$')) {
      mathContent = input.replace(/\$(.*?)\$/g, '$1');
      displayMode = false;
    }

    const rendered = katex.renderToString(mathContent, {
      displayMode: displayMode,
      throwOnError: false,
      output: 'html',
      strict: false
    });

    klgNameRendered.value = rendered;
  } catch (error) {
    console.error('知识名称KaTeX渲染错误:', error);
    klgNameRendered.value = `<span style="color: red;">渲染错误: ${input}</span>`;
  }
};

const handleKlgNameBlur = () => {
  if (infoForm.value.klgName.trim() && klgNameRendered.value) {
    klgNameEditMode.value = false;
  }
};

const handleKlgNamePreviewClick = () => {
  klgNameEditMode.value = true;
  nextTick(() => {
    if (klgNameInputRef.value) {
      klgNameInputRef.value.focus();
    }
  });
};

const handleKlgNameFocus = () => {
  klgNameEditMode.value = true;
};

// 同义词相关处理函数
const renderSynonym = (input: string, index: number) => {
  if (!input.trim()) {
    synonymRendered.value[index] = '';
    return;
  }

  try {
    let mathContent = input;
    let displayMode = false;

    if (input.includes('$$')) {
      mathContent = input.replace(/\$\$(.*?)\$\$/g, '$1');
      displayMode = true;
    } else if (input.includes('$')) {
      mathContent = input.replace(/\$(.*?)\$/g, '$1');
      displayMode = false;
    }

    const rendered = katex.renderToString(mathContent, {
      displayMode: displayMode,
      throwOnError: false,
      output: 'html',
      strict: false
    });

    synonymRendered.value[index] = rendered;
  } catch (error) {
    console.error(`同义词${index}KaTeX渲染错误:`, error);
    synonymRendered.value[index] = `<span style="color: red;">渲染错误: ${input}</span>`;
  }
};

const handleSynonymBlur = (index: number) => {
  const synonymValue = infoForm.value.synonymList[index];
  if (synonymValue && synonymValue.trim() && synonymRendered.value[index]) {
    synonymEditModes.value[index] = false;
  }
};

const handleSynonymPreviewClick = (index: number) => {
  synonymEditModes.value[index] = true;
  nextTick(() => {
    if (synonymInputRefs.value[index]) {
      synonymInputRefs.value[index].focus();
    }
  });
};

const handleSynonymFocus = (index: number) => {
  synonymEditModes.value[index] = true;
};

// 处理选择改变
const handleChangeOption = () => {
  if (KlgTypeDict[infoForm.value.klgType] === KlgType.Principles) {
    emits('showStep3', true);
  } else {
    emits('showStep3', false);
  }
};
// 处理添加同义词
const handleAddSynonym = () => {
  infoForm.value.synonymList.push('');
};
// 处理移除同义词
const handleRemoveSynonym = (target: string) => {
  const index = infoForm.value.synonymList.indexOf(target);
  if (index !== -1) {
    infoForm.value.synonymList.splice(index, 1);
  }
};
// 处理取消
const handleCancel = () => {
  const curNode = sessionStorage.getItem('currentNode');
  if (curNode) {
    const node = JSON.parse(curNode);
    router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
  } else {
    const defaultNode = sessionStorage.getItem('defaultNode');
    if (defaultNode) {
      const node = JSON.parse(defaultNode);
      router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
    } else {
      router.push(`klg/maintain`);
    }
  }
};
// 处理打开编者规范
const handleOpenRule = () => {
  window.open('/rule', '_blank');
};
// 处理下一步
const handleNextStep = () => {
  infoFormRef.value?.validate((valid) => {
    if (valid) {
      handleSave().then((result) => {
        if (result) {
          emits('step', 1);
          router.replace({
            query: {
              ...route.query,
              klgCode: curKlgCode.value
            }
          });
          const data = {
            klgCode: curKlgCode.value,
            step: 1
          };
          emits('refresh', data);
        }
      });
    }
  });
};
// 处理保存
const handleSave = async (): Promise<boolean> => {
  return new Promise<boolean>((resolve) => {
    infoFormRef.value?.validateField('klgName', async (valid) => {
      if (!valid) {
        resolve(false);
        return;
      }

      const area = sessionStorage.getItem('area');
      const params: params2EditBaseInfo = {
        klgCode: infoForm.value.klgCode,
        sortId: KlgTypeDict[infoForm.value.klgType],
        title: infoForm.value.klgName,
        sysTitles: infoForm.value.synonymList.join('@@'),
        // notice: infoForm.value.note,
        notice: convertLanguageMathToScript(convertImgTagLongUrls(editorNote.value.getHtml())),
        areaCodes: area ? [area] : []
      };

      try {
        const res = await editBaseInfoApi(params);
        if (res.success) {
          curKlgCode.value = res.data.klgCode;
          router.replace({
            query: {
              ...route.query,
              klgCode: curKlgCode.value
            }
          });
          ElMessage.success('保存成功');
          resolve(true);
        } else {
          ElMessage.error(res.message);
          resolve(false);
        }
      } catch (error) {
        console.error(`Error: ${error}`);
        resolve(false);
      }
    });
  });
};
watch(
  () => props.detail,
  async () => {
    if (props.detail) {
      infoForm.value.klgCode = props.detail.klgCode;
      infoForm.value.klgName = props.detail.title;
      infoForm.value.klgType = props.detail.sortTitle;

      if (props.detail.notice && editorNote.value?.html2MdWhenReady) {
        // 使用新的异步转换方法
        infoForm.value.note = await editorNote.value.html2MdWhenReady(props.detail.notice);
      }

      if (props.detail.sysTitles === '' || !props.detail.sysTitles) {
        infoForm.value.synonymList = [];
      } else {
        infoForm.value.synonymList = props.detail.sysTitles.split('@@');
      }
    }
  },
  { deep: true, immediate: true }
);
</script>

<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <el-steps class="header-steps" :active="0" finish-status="success" align-center>
        <el-step title="基础信息" />
        <el-step title="内容信息" />
        <el-step v-if="props.showStep3" title="论证信息" />
      </el-steps>

      <!-- KaTeX 实时渲染区域 -->
      <div class="math-input-container">
        <div class="math-input-section">
          <label class="math-label">数学公式输入：</label>

          <!-- 编辑模式：显示输入框 -->
          <el-input
            v-if="isEditMode"
            ref="mathInputRef"
            v-model="mathInput"
            type="textarea"
            :rows="3"
            placeholder="请输入数学公式，支持 $行内公式$ 和 $$行间公式$$"
            class="math-input"
            @blur="handleInputBlur"
            @focus="handleInputFocus"
          />

          <!-- 预览模式：显示渲染结果 -->
          <div
            v-else-if="renderedMath"
            class="math-preview-container"
            @click="handlePreviewClick"
          >
            <div class="math-preview" v-html="renderedMath"></div>
            <div class="preview-hint">点击编辑</div>
          </div>
        </div>
      </div>
      <span class="header-tips" @click="handleOpenRule">编者规范</span>
    </div>
    <div class="line"></div>
    <div class="form-container">
      <el-form ref="infoFormRef" label-postion="right" label-width="100px" :model="infoForm">
        <el-form-item
          label="知识类型"
          prop="klgType"
          required
          :rules="{
            required: true,
            message: '请输入知识类型',
            trigger: 'blur'
          }"
        >
          <el-select
            v-model="infoForm.klgType"
            style="width: 400px; margin-top: 7px"
            @change="handleChangeOption"
          >
            <el-option
              v-for="(key, value) in KlgTypeDict"
              :key="key"
              :value="value"
              class="primary"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="知识名称"
          prop="klgName"
          required
          :rules="{
            required: true,
            message: '请输入知识名称',
            trigger: 'blur'
          }"
        >
          <!-- 编辑模式：显示输入框 -->
          <el-input
            v-if="klgNameEditMode"
            ref="klgNameInputRef"
            v-model="infoForm.klgName"
            placeholder="请输入知识名称，支持数学公式"
            style="width: 1036px; height: 36px"
            @blur="handleKlgNameBlur"
            @focus="handleKlgNameFocus"
          />

          <!-- 预览模式：显示渲染结果 -->
          <div
            v-else-if="klgNameRendered"
            class="klg-name-preview-container"
            @click="handleKlgNamePreviewClick"
            style="width: 1036px; min-height: 36px"
          >
            <div class="klg-name-preview" v-html="klgNameRendered"></div>
            <div class="preview-hint">点击编辑</div>
          </div>
        </el-form-item>
        <el-form-item label="同义词" prop="synonymList">
          <el-form-item>
            <span class="add-block" @click="handleAddSynonym">
              <img src="@/assets/image/add.svg" /><span class="add-text">添加同义词</span>
            </span>
          </el-form-item>
          <div
            v-for="(item, index) in infoForm.synonymList"
            :key="index"
            style="width: 100%; margin-bottom: 14px"
          >
            <el-form-item
              style="width: 100%"
              :prop="`synonymList.${index}`"
              :rules="{
                required: true,
                message: '请输入同义词',
                trigger: 'blur'
              }"
            >
              <div style="width: 100%; display: flex">
                <!-- 编辑模式：显示输入框 -->
                <el-input
                  v-if="synonymEditModes[index]"
                  :ref="(el) => synonymInputRefs[index] = el"
                  v-model="infoForm.synonymList[index]"
                  placeholder="请输入同义词，支持数学公式"
                  style="margin-top: 7px; flex: 1"
                  @blur="handleSynonymBlur(index)"
                  @focus="handleSynonymFocus(index)"
                />

                <!-- 预览模式：显示渲染结果 -->
                <div
                  v-else-if="synonymRendered[index]"
                  class="synonym-preview-container"
                  @click="handleSynonymPreviewClick(index)"
                  style="margin-top: 7px; flex: 1"
                >
                  <div class="synonym-preview" v-html="synonymRendered[index]"></div>
                  <div class="preview-hint">点击编辑</div>
                </div>

                <span class="rm-block" @click="handleRemoveSynonym(item.toString())"
                  ><img src="@/assets/image/rm.svg"
                /></span>
              </div>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="编者笔记">
          <div style="width: 98%; height: 400px" class="note-block">
            <div class="add-block">
              <img src="@/assets/image/klg/u2852.svg" />
              <span class="add-text">画图</span>
            </div>
            <classicCKEditor
              v-model="infoForm.note"
              :height="380"
              ref="editorNote"
            ></classicCKEditor>
          </div>
        </el-form-item>
      </el-form>
      <div class="footer">
        <CmpButton type="info" @click="handleCancel">取消</CmpButton>
        <CmpButton type="primary" @click="handleSave">存草稿</CmpButton>
        <CmpButton type="primary" @click="handleNextStep">下一步</CmpButton>
      </div>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  width: 1200px;
  min-height: 750px;
  font-family: var(--text-family);
  background-color: white;
  .header-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    position: relative;
    /* el-steps 进行状态 */
    ::v-deep(.is-process) {
      color: var(--color-black);
      font-weight: 400;
      font-size: 14px;
    }
    /* el-steps 等待状态 */
    ::v-deep(.is-wait) {
      color: var(--color-invalid);
      font-weight: 400;
      font-size: 14px;
    }
    .header-steps {
      --el-color-success: var(--color-black);
      margin-top: 10px;
      width: 400px;
    }
    .header-tips {
      color: var(--color-primary);
      font-size: 12px;
      cursor: pointer;
      position: absolute;
      right: 40px;
      bottom: 5px;
      &:hover {
        font-weight: 600;
      }
    }
  }
  .form-container {
    padding: 10px;
    width: 98%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .note-block {
      height: 400px;
    }
    .add-block {
      display: flex;
      align-items: center;
      cursor: pointer;
      .add-text {
        margin-left: 5px;
        color: var(--color-primary);
        font-size: 12px;
      }
    }
    .rm-block {
      margin-left: 5px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    .footer {
      width: 100%;
      display: flex;
      justify-content: center;
      gap: 40px;
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
  margin-bottom: 15px;
}

/* KaTeX 实时渲染样式 */
.math-input-container {
  margin: 20px 40px;
  padding: 20px;
  border: 1px solid var(--color-boxborder);
  border-radius: 8px;
  background-color: #fafafa;
}

.math-input-section,
.math-output-section {
  margin-bottom: 15px;
}

.math-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--color-black);
  font-size: 14px;
}

.math-input {
  width: 100%;
}

.math-output {
  min-height: 40px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  font-size: 16px;
  line-height: 1.6;
}

/* KaTeX 渲染结果样式优化 */
.math-output .katex {
  font-size: 1.1em;
}

.math-output .katex-display {
  margin: 10px 0;
  text-align: center;
}

/* 预览模式样式 */
.math-preview-container {
  position: relative;
  min-height: 80px;
  padding: 12px;
  border: 2px dashed #d0d0d0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.math-preview-container:hover {
  border-color: var(--color-primary);
  background-color: #f8f9ff;
}

.math-preview {
  font-size: 16px;
  line-height: 1.6;
  min-height: 40px;
}

.preview-hint {
  position: absolute;
  top: 8px;
  right: 12px;
  font-size: 12px;
  color: #999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.math-preview-container:hover .preview-hint {
  opacity: 1;
}

/* KaTeX 在预览模式中的样式 */
.math-preview .katex {
  font-size: 1.1em;
}

.math-preview .katex-display {
  margin: 10px 0;
  text-align: center;
}

/* 知识名称预览模式样式 */
.klg-name-preview-container {
  position: relative;
  padding: 8px 12px;
  border: 2px dashed #d0d0d0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.klg-name-preview-container:hover {
  border-color: var(--color-primary);
  background-color: #f8f9ff;
}

.klg-name-preview {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.klg-name-preview-container .preview-hint {
  position: absolute;
  top: 4px;
  right: 8px;
  font-size: 10px;
  color: #999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.klg-name-preview-container:hover .preview-hint {
  opacity: 1;
}

/* 同义词预览模式样式 */
.synonym-preview-container {
  position: relative;
  padding: 8px 12px;
  border: 2px dashed #d0d0d0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-height: 36px;
}

.synonym-preview-container:hover {
  border-color: var(--color-primary);
  background-color: #f8f9ff;
}

.synonym-preview {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.synonym-preview-container .preview-hint {
  position: absolute;
  top: 4px;
  right: 8px;
  font-size: 10px;
  color: #999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.synonym-preview-container:hover .preview-hint {
  opacity: 1;
}

/* KaTeX 在知识名称和同义词中的样式 */
.klg-name-preview .katex,
.synonym-preview .katex {
  font-size: 1em;
}

.klg-name-preview .katex-display,
.synonym-preview .katex-display {
  margin: 5px 0;
  text-align: left;
}
</style>
